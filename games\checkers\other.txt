/content/AlphaZero.jl# sudo apt-get update
sudo apt-get install -y \
    libgl1        \
    libgl1-mesa-glx \
    libglu1-mesa  \
    libglvnd0     \
    libglapi-mesa
Hit:1 https://cloud.r-project.org/bin/linux/ubuntu jammy-cran40/ InRelease
Hit:2 https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2204/x86_64  InRelease                  
Hit:3 http://security.ubuntu.com/ubuntu jammy-security InRelease                                             
Hit:4 http://archive.ubuntu.com/ubuntu jammy InRelease                                                       
Hit:5 http://archive.ubuntu.com/ubuntu jammy-updates InRelease                                               
Hit:6 http://archive.ubuntu.com/ubuntu jammy-backports InRelease                                        
Hit:7 https://r2u.stat.illinois.edu/ubuntu jammy InRelease                                             
Hit:8 https://ppa.launchpadcontent.net/deadsnakes/ppa/ubuntu jammy InRelease     
Hit:9 https://ppa.launchpadcontent.net/graphics-drivers/ppa/ubuntu jammy InRelease
Hit:10 https://ppa.launchpadcontent.net/ubuntugis/ppa/ubuntu jammy InRelease
Reading package lists... Done
W: Skipping acquire of configured file 'main/source/Sources' as repository 'https://r2u.stat.illinois.edu/ubuntu jammy InRelease' does not seem to provide it (sources.list entry misspelt?)
Reading package lists... Done
Building dependency tree... Done
Reading state information... Done
libgl1 is already the newest version (1.4.0-1).
libglu1-mesa is already the newest version (9.0.2-1).
libglvnd0 is already the newest version (1.4.0-1).
libglapi-mesa is already the newest version (23.2.1-1ubuntu3.1~22.04.3).
libglapi-mesa set to manually installed.
libgl1-mesa-glx is already the newest version (23.0.4-0ubuntu1~22.04.1).
0 upgraded, 0 newly installed, 0 to remove and 35 not upgraded.
/content/AlphaZero.jl# nm -D /usr/lib/x86_64-linux-gnu/libglapi.so.* | grep _glapi_tls_Current
# should show: T _glapi_tls_Current
/content/AlphaZero.jl# julia --threads=auto -e 'using Pkg; Pkg.activate("."); Pkg.precompile()'
  Activating environment at `/content/AlphaZero.jl/Project.toml`
Precompiling project...

  ✗ libdecor_jll
  ✗ Qt6Base_jll
  ✗ GLFW_jll
  ✗ GR_jll
  ✗ GR
  ✗ Plots
  ◑ AlphaZero




  ✗ libdecor_jll
  ✗ Qt6Base_jll
  ✗ GLFW_jll














=================>     ]  6/7






















=================>     ]  6/7






















=================>     ]  6/7






















=================>     ]  6/7






















=================>     ]  6/7






















=================>     ]  6/7






















=================>     ]  6/7






















  Progress [===================================>     ]  6/7














  ✗ libdecor_jll
  ✗ Qt6Base_jll
  ✗ GLFW_jll
  ✗ GR_jll
  ✗ GR
  ✗ Plots
  ✗ AlphaZero
  0 dependencies successfully precompiled in 74 seconds (243 already precompiled)

ERROR: 
AlphaZero [8ed9eb0b-7496-408d-8c8b-2119aeea02cd]

Failed to precompile AlphaZero [8ed9eb0b-7496-408d-8c8b-2119aeea02cd] to /root/.julia/compiled/v1.6/AlphaZero/jl_cBP785.
[ Info: Using the Flux implementation of AlphaZero.NetLib.
ERROR: LoadError: LoadError: InitError: could not load library "/root/.julia/artifacts/52d9b3e9e3507f7b2cf723af43d0e7f095e2edc7/lib/libGL.so"
/root/.julia/artifacts/52d9b3e9e3507f7b2cf723af43d0e7f095e2edc7/lib/libGL.so: undefined symbol: _glapi_tls_Current
Stacktrace:
  [1] dlopen(s::String, flags::UInt32; throw_error::Bool)
    @ Base.Libc.Libdl ./libdl.jl:114
  [2] dlopen(s::String, flags::UInt32)
    @ Base.Libc.Libdl ./libdl.jl:114
  [3] macro expansion
    @ ~/.julia/packages/JLLWrappers/m2Pjh/src/products/library_generators.jl:63 [inlined]
  [4] __init__()
    @ Libglvnd_jll ~/.julia/packages/Libglvnd_jll/upkN8/src/wrappers/x86_64-linux-gnu.jl:22
  [5] _include_from_serialized(path::String, depmods::Vector{Any})
    @ Base ./loading.jl:696
  [6] _require_search_from_serialized(pkg::Base.PkgId, sourcepath::String)
    @ Base ./loading.jl:782
  [7] _require(pkg::Base.PkgId)
    @ Base ./loading.jl:1020
  [8] require(uuidkey::Base.PkgId)
    @
 [10] include(mod::Module, _path::String)
    @ Base ./Base.jl:384
 [11] top-level scope
    @ ~/.julia/packages/JLLWrappers/m2Pjh/src/toplevel_generators.jl:199
 [12] include
    @ ./Base.jl:384 [inlined]
 [13] include_package_for_output(pkg::Base.PkgId, input::String, depot_path::Vector{String}, dl_load_path::Vector{String}, load_path::Vector{String}, concrete_deps::Vector{Pair{Base.PkgId, UInt64}}, source::String)
    @ Base ./loading.jl:1235
 [14] top-level scope
    @ none:1
 [15] eval
    @ ./boot.jl:360 [inlined]
 [16] eval(x::Expr)
    @ Base.MainInclude ./client.jl:446
 [17] top-level scope
    @ none:1
during initialization of module Libglvnd_jll
in expression starting at /root/.julia/packages/libdecor_jll/Ha22M/src/wrappers/x86_64-linux-gnu.jl:6
in expression starting at /root/.julia/packages/libdecor_jll/Ha22M/src/libdecor_jll.jl:2
ERROR: LoadError: LoadError: Failed to precompile libdecor_jll [1183f4f0-6f2a-5f1a-908b-139f9cdfea6f] to /root/.julia/compiled/v1.6/libdecor_jll/jl_bgr5gy.
Stacktrace:
  [1] error(s::String)
    @ Base ./error.jl:33
  [2] compilecache(pkg::Base.PkgId, path::String, internal_stderr::IOContext{Base.PipeEndpoint}, internal_stdout::IOContext{IOStream}, ignore_loaded_modules::Bool)
    @ Base ./loading.jl:1385
  [3] compilecache(pkg::Base.PkgId, path::String)
    @ Base ./loading.jl:1329
  [4] _require(pkg::Base.PkgId)
    @ Base ./loading.jl:1043
  [5] require(uuidkey::Base.PkgId)
    @ Base ./loading.jl:936
  [6] require(into::Module, mod::Symbol)
    @ Base ./loading.jl:923
  [7] include(mod::Module, _path::String)
    @ Base ./Base.jl:384
  [8] top-level scope
    @ ~/.julia/packages/JLLWrappers/m2Pjh/src/toplevel_generators.jl:199
  [9] include
    @ ./Base.jl:384 [inlined]
 [10] include_package_for_output(pkg::Base.PkgId, input::String, depot_path::Vector{String}, dl_load_path::Vector{String}, load_path::Vector{String}, concrete_deps::Vector{Pair{Base.PkgId, UInt64}}, source::String)
    @ Base ./loading.jl:1235
 [11] top-level scope
    @ none:1
 [12] eval
    @ ./boot.jl:360 [inlined]
 [13] eval
    @ Base.MainInclude ./client.jl:446
 [14] top-level scope
    @ none:1
in expression starting at /root/.julia/packages/GLFW_jll/tqbf0/src/wrappers/x86_64-linux-gnu.jl:4
in expression starting at /root/.julia/packages/GLFW_jll/tqbf0/src/GLFW_jll.jl:2
ERROR: LoadError: LoadError: Failed to precompile GLFW_jll [0656b61e-2033-5cc2-a64a-77c0f6c09b89] to /root/.julia/compiled/v1.6/GLFW_jll/jl_noTQBF.
Stacktrace:
  [1] error(s::String)
    @ Base ./error.jl:33
  [2] compilecache(pkg::Base.PkgId, path::String, internal_stderr::IOContext{Base.PipeEndpoint}, internal_stdout::IOContext{IOStream}, ignore_loaded_modules::Bool)
    @ Base ./loading.jl:1385
  [3] compilecache(pkg::Base.PkgId, path::String)
    @ Base ./loading.jl:1329
  [4] _require(pkg::Base.PkgId)
    @ Base ./loading.jl:1043
  [5] require(uuidkey::Base.PkgId)
    @ Base ./loading.jl:936
  [6] require(into::Module, mod::Symbol)
    @ Base ./loading.jl:923
  [7] include(mod::Module, _path::String)
    @ Base ./Base.jl:384
  [8] top-level scope
    @ ~/.julia/packages/JLLWrappers/m2Pjh/src/toplevel_generators.jl:199
  [9] include
    @ ./Base.jl:384 [inlined]
 [10] include_package_for_output(pkg::Base.PkgId, input::String, depot_path::Vector{String}, dl_load_path::Vector{String}, load_path::Vector{String}, concrete_deps::Vector{Pair{Base.PkgId, UInt64}}, source::String)
    @ Base ./loading.jl:1235
 [11] top-level scope
    @ none:1
 [12] eval
    @ ./boot.jl:360 [inlined]
 [13] eval(x::Expr)
    @ Base.MainInclude ./client.jl:446
 [14] top-level scope
    @ none:1
in expression starting at /root/.julia/packages/GR_jll/btQNT/src/wrappers/x86_64-linux-gnu-cxx11.jl:9
in expression starting at /root/.julia/packages/GR_jll/btQNT/src/GR_jll.jl:2
┌ Error: import GR_jll failed.
│ Consider using `GR.GRPreferences.use_jll_binary()` or
│ `GR.GRPreferences.use_upstream_binary()` to repair.
│ Importing GR a second time will allow use of these functions.
└ @ GR.GRPreferences ~/.julia/packages/GR/JBzkS/src/preferences.jl:8
ERROR: LoadError: LoadError: Failed to precompile GR_jll [d2c73de3-f751-5644-a686-071e5b155ba9] to /root/.julia/compiled/v1.6/GR_jll/jl_enzsF7.
Stacktrace:
  [1] error(s::String)
    @ Base ./error.jl:33
  [2] compilecache(pkg::Base.PkgId, path::String, internal_stderr::IOContext{Base.PipeEndpoint}, internal_stdout::IOContext{IOStream}, igno
    @ Base ./loading.jl:1385
  [3] compilecache(pkg::Base.PkgId, path::String)
    @ Base ./loading.jl:1329
  [4] _require(pkg::Base.PkgId)
    @ Base ./loading.jl:1043
  [5] require(uuidkey::Base.PkgId)
    @ Base ./loading.jl:936
  [6] require(into::Module, mod::Symbol)
    @ Base ./loading.jl:923
  [7] top-level scope
    @ ~/.julia/packages/GR/JBzkS/src/preferences.jl:6
  [8] include(mod::Module, _path::String)
    @ Base ./Base.jl:384
  [9] include(x::String)
    @ GR ~/.julia/packages/GR/JBzkS/src/GR.jl:20
 [10] top-level scope
    @ ~/.julia/packages/GR/JBzkS/src/GR.jl:268
 [11] include
    @ ./Base.jl:384 [inlined]
 [12] include_package_for_output(pkg::Base.PkgId, input::String, depot_path::Vector{String}, dl_load_path::Vector{String}, load_path::Vector{String}, concrete_deps::Vector{Pair{Base.PkgId, UInt64}}, source::String)
    @ Base ./loading.jl:1235
 [13] top-level scope
    @ none:1
 [14] eval
    @ ./boot.jl:360 [inlined]
 [16] top-level scope
    @ none:1
in expression starting at /root/.julia/packages/GR/JBzkS/src/preferences.jl:1
in expression starting at /root/.julia/packages/GR/JBzkS/src/GR.jl:1
ERROR: LoadError: LoadError: Failed to precompile GR [28b8d3ca-fb5f-59d9-8090-bfdbd6d07a71] to /root/.julia/compiled/v1.6/GR/jl_1b8Pqu.
Stacktrace:
  [1] error(s::String)
    @ Base ./error.jl:33
  [2] compilecache(pkg::Base.PkgId, path::String, internal_stderr::IOContext{Base.PipeEndpoint}, internal_stdout::IOContext{IOStream}, ignore_loaded_modules::Bool)
    @ Base ./loading.jl:1385
  [3] compilecache(pkg::Base.PkgId, path::String)
    @ Base ./loading.jl:1329
  [4] _require(pkg::Base.PkgId)
    @ Base ./loading.jl:1043
  [5] require(uuidkey::Base.PkgId)
    @ Base ./loading.jl:936
  [6] require(into::Module, mod::Symbol)
    @ Base ./loading.jl:923
  [7] top-level scope
    @ ~/.julia/packages/Plots/gYkEG/src/backends.jl:381
  [8] eval
    @ ./boot.jl:360 [inlined]
  [9] _initialize_backend(pkg::Plots.GRBackend)
    @ Plots 
 [10] backend(pkg::Plots.GRBackend)
    @ Plots ~/.julia/packages/Plots/gYkEG/src/backends.jl:245
 [11] backend(sym::Symbol)
    @ Plots ~/.julia/packages/Plots/gYkEG/src/backends.jl:255
 [12] load_default_backend()
    @ Plots ~/.julia/packages/Plots/gYkEG/src/backends.jl:200
 [13] backend()
    @ Plots ~/.julia/packages/Plots/gYkEG/src/backends.jl:233
 [14] top-level scope
    @ ~/.julia/packages/Plots/gYkEG/src/init.jl:110
 [15] include(mod::Module, _path::String)
    @ Base ./Base.jl:384
 [16] include(x::String)
    @ Plots ~/.julia/packages/Plots/gYkEG/src/Plots.jl:1
 [17] top-level scope
    @ ~/.julia/packages/Plots/gYkEG/src/Plots.jl:176
 [18] include
    @ ./Base.jl:384 [inlined]
 [19] include_package_for_output(pkg::Base.PkgId, input::String, depot_path::Vector{String}, dl_load_path::Vector{String}, load_path::Vector{String}, concrete_deps::Vector{Pair{Base.PkgId, UInt64}}, source::String)
    @ Base ./loading.jl:1235
 [20] top-level scope
    @ none:1
 [21] eval
    @ ./boot.jl:360 [inlined]
 [22] eval(x::Expr)
    @ Base.MainInclude ./client.jl:446
 [23] top-level scope
    @ none:1
in expression starting at /root/.julia/packages/Plots/gYkEG/src/init.jl:110
in expression starting at /root/.julia/packages/Plots/gYkEG/src/Plots.jl:1
ERROR: LoadError: LoadError: Failed to precompile Plots [91a5bcdd-55d7-5caf-9e0b-520d859cae80] to /root/.julia/compiled/v1.6/Plots/jl_eIRSBg.
Stacktrace:
  [1] error(s::String)
    @ Base ./error.jl:33
  [2] compilecache(pkg::Base.PkgId, path::String, internal_stderr::IOContext{Base.PipeEndpoint}, internal_stdout::IOContext{IOStream}, ignore_loaded_modules::Bool)
    @ Base ./loading.jl:1385
  [3] compilecache(pkg::Base.PkgId, path::String)
    @ Base ./loading.jl:1329
  [4] _require(pkg::Base.PkgId)
    @ Base ./loading.jl:1043
  [5] require(uuidkey::Base.PkgId)
    @ Base ./loading.jl:936
  [6] require(into::Module, mod::Symbol)
    @ Base ./loading.jl:923
  [7] include(mod::Module, _path::String)
    @ Base ./Base.jl:384
  [8] include(x::String)
    @ AlphaZero /content/AlphaZero.jl/src/Alp
    @ Base ./loading.jl:1235
 [12] top-level scope
    @ none:1
 [13] eval
    @ ./boot.jl:360 [inlined]
 [14] eval(x::Expr)
    @ Base.MainInclude ./client.jl:446
 [15] top-level scope
    @ none:1
in expression starting at /content/AlphaZero.jl/src/ui/ui.jl:1
in expression starting at /content/AlphaZero.jl/src/AlphaZero.jl:6

Plots [91a5bcdd-55d7-5caf-9e0b-520d859cae80]

Failed to precompile Plots [91a5bcdd-55d7-5caf-9e0b-520d859cae80] to /root/.julia/compiled/v1.6/Plots/jl_8bidOt.
ERROR: LoadError: LoadError: InitError: could not load library "/root/.julia/artifacts/52d9b3e9e3507f7b2cf723af43d0e7f095e2edc7/lib/libGL.so"
/root/.julia/artifacts/52d9b3e9e3507f7b2cf723af43d0e7f095e2edc7/lib/libGL.so: undefined symbol: _glapi_tls_Current
Stacktrace:
  [1] dlopen(s::String, flags::UInt32; throw_error::Bool)
    @ Base.Libc.Libdl ./libdl.jl:114
  [2] dlopen(s::String, flags::UInt32)
    @ Base.Libc.Libdl ./libdl.jl:114
  [3] macro expansion
    @ ~/.julia/packages/JLLWrappers/m2Pjh/src/products/library_generators.jl:63 [inlined]
  [4] __init__()
    @ Libglvnd_jll ~/.julia/packages/Libglvnd_jll/upkN8/src/wrappers/x86_64-linux-gnu.jl:22
  [5] _include_from_serialized(path::String, depmods::Vector{Any})
    @ Base ./loading.jl:696
  [6] _require_search_from_serialized(pkg::Base.PkgId, sourcepath::String)
    @ Base ./loading.jl:782
  [7] _require(pkg::Base.PkgId)
    @ Base ./loading.jl:1020
  [8] require(uuidkey::Base.PkgId)
    @ Base ./loading.jl:936
  [9] require(into::Module, mod::Symbol)
    @ Base ./loading.jl:923
 [10] include(mod::Module, _path::String)
    @ Base ./Base.jl:384
 [11] top-level scope
    @ ~/.julia/packages/JLLWrappers/m2Pjh/src/toplevel_generators.jl:199
 [12] include
    @ ./Base.jl:384 [inlined]
 [13] include_package_for_output(pkg::Base.PkgId, input::String, depot_path::Vector{String}, dl_load_path::Vector{String}, load_path::Vector{String}, concrete_deps::Vector{Pair{Base.PkgId, UInt64}}, source::String)
    @ 
 [15] eval
    @ ./boot.jl:360 [inlined]
 [16] eval(x::Expr)
    @ Base.MainInclude ./client.jl:446
 [17] top-level scope
    @ none:1
during initialization of module Libglvnd_jll
in expression starting at /root/.julia/packages/libdecor_jll/Ha22M/src/wrappers/x86_64-linux-gnu.jl:6
in expression starting at /root/.julia/packages/libdecor_jll/Ha22M/src/libdecor_jll.jl:2
ERROR: LoadError: LoadError: Failed to precompile libdecor_jll [1183f4f0-6f2a-5f1a-908b-139f9cdfea6f] to /root/.julia/compiled/v1.6/libdecor_jll/jl_P39UW0.
Stacktrace:
  [1] error(s::String)
    @ Base ./error.jl:33
  [2] compilecache(pkg::Base.PkgId, path::String, internal_stderr::IOContext{Base.PipeEndpoint}, internal_stdout::IOContext{IOStream}, ignore_loaded_modules::Bool)
    @ Base ./loading.jl:1385
  [3] compilecache(pkg::Base.PkgId, path::String)
    @ Base ./loading.jl:1329
  [4] _require(pkg::Base.PkgId)
    @ Base ./loading.jl:1043
  [5] require(uuidkey::Base.PkgId)
    @ Base ./loading.jl:936
  [6] require(into::Module, mod::Symbol)
    @ Base ./loading.jl:923
  [7] include
    @ Base ./Base.jl:384
  [8] top-level scope
    @ ~/.julia/packages/JLLWrappers/m2Pjh/src/toplevel_generators.jl:199
  [9] include
    @ ./Base.jl:384 [inlined]
 [10] include_package_for_output(pkg::Base.PkgId, input::String, depot_path::Vector{String}, dl_load_path::Vector{String}, load_path::Vector{String}, concrete_deps::Vector{Pair{Base.PkgId, UInt64}}, source::String)
    @ Base ./loading.jl:1235
 [11] top-level scope
    @ none:1
 [12] eval
    @ ./boot.jl:360 [inlined]
 [13] eval(x::Expr)
    @ Base.MainInclude ./client.jl:446
 [14] top-level scope
    @ none:1
in expression starting at /root/.julia/packages/GLFW_jll/tqbf0/src/wrappers/x86_64-linux-gnu.jl:4
in expression starting at /root/.julia/packages/GLFW_jll/tqbf0/src/GLFW_jll.jl:2
ERROR: LoadError: LoadError: Failed to precompile GLFW_jll [0656b61e-2033-5cc2-a64a-77c0f6c09b89] to /root/.julia/compiled/v1.6/GLFW_jll/jl_5tikyc.
Stacktrace:
  [1] error(s::String)
    @ Base ./error.jl:33
  [2] compilecache(pkg::Base.PkgId, path::String, internal_stderr::IOContext{Base.PipeEndpoint}, internal_stdout::IOContext{IOStream}, ignore_loaded_modules::Bool)
    @ Base ./loading.jl:1385
    @ Base ./loading.jl:1329
  [4] _require(pkg::Base.PkgId)
    @ Base ./loading.jl:1043
  [5] require(uuidkey::Base.PkgId)
    @ Base ./loading.jl:936
  [6] require(into::Module, mod::Symbol)
    @ Base ./loading.jl:923
  [7] include(mod::Module, _path::String)
    @ Base ./Base.jl:384
  [8] top-level scope
    @ ~/.julia/packages/JLLWrappers/m2Pjh/src/toplevel_generators.jl:199
  [9] include
    @ ./Base.jl:384 [inlined]
 [10] include_package_for_output(pkg::Base.PkgId, input::String, depot_path::Vector{String}, dl_load_path::Vector{String}, load_path::Vector{String}, concrete_deps::Vector{Pair{Base.PkgId, UInt64}}, source::String)
    @ Base ./loading.jl:1235
 [11] top-level scope
    @ none:1
 [12] eval
    @ ./boot.jl:360 [inlined]
 [13] eval(x::Expr)
    @ Base.MainInclude ./client.jl:446
 [14] top-level scope
    @ none:1
in expression starting at /root/.julia/packages/GR_jll/btQNT/src/wrappers/x86_64-linux-gnu-cxx11.jl:9
in expression starting at /root/.julia/packages/GR_jll/btQNT/src/GR_jll.jl:2
┌ Error: import GR_jll failed.
│ Consider using `GR.GRPreferences.use_jll_binary()` or
│ `GR.GRPreferences.use_upstream_binary()` to repair.
│ Importing GR a second time will allow use of these functions.
└ @ GR.GRPreferences ~/.julia/packages/GR/JBzkS/src/preferences.jl:8
ERROR: LoadError: LoadError: Failed to precompile GR_jll [d2c73de3-f751-5644-a686-071e5b155ba9] to /root/.julia/compiled/v1.6/GR_jll/jl_UOJR1L.
Stacktrace:
  [1] error(s::String)
    @ Base ./error.jl:33
  [2] compilecache(pkg::Base.PkgId, path::String, internal_stderr::IOContext{Base.PipeEndpoint}, internal_stdout::IOContext{IOStream}, ignore_loaded_modules::Bool)
    @ Base ./loading.jl:1385
  [3] compilecache(pkg::Base.PkgId, path::String)
    @ Base ./loading.jl:1329
  [4] _require(pkg::Base.PkgId)
    @ Base ./loading.jl:1043
  [5] require(uuidkey::Base.PkgId)
    @ Base ./loading.jl:936
  [6] require(into::Module, mod::Symbol)
    @ Base ./loading.jl:923
  [7] top-level scope
    @ ~/.julia/packages/GR/JBzkS/src/preferences.jl:6
  [8] include(mod::Module, _path::String)
    @ Base .
 [10] top-level scope
    @ ~/.julia/packages/GR/JBzkS/src/GR.jl:268
 [11] include
    @ ./Base.jl:384 [inlined]
 [12] include_package_for_output(pkg::Base.PkgId, input::String, depot_path::Vector{String}, dl_load_path::Vector{String}, load_path::Vector{String}, concrete_deps::Vector{Pair{Base.PkgId, UInt64}}, source::String)
    @ Base ./loading.jl:1235
 [13] top-level scope
    @ none:1
 [14] eval
    @ ./boot.jl:360 [inlined]
 [15] eval(x::Expr)
    @ Base.MainInclude ./client.jl:446
 [16] top-level scope
    @ none:1
in expression starting at /root/.julia/packages/GR/JBzkS/src/preferences.jl:1
in expression starting at /root/.julia/packages/GR/JBzkS/src/GR.jl:1
ERROR: LoadError: LoadError: Failed to precompile GR [28b8d3ca-fb5f-59d9-8090-bfdbd6d07a71] to /root/.julia/compiled/v1.6/GR/jl_fkpGS5.
Stacktrace:
  [1] error(s::String)
    @ Base ./error.jl:33
  [2] compilecache(pkg::Base.PkgId, path::String, internal_stderr::IOContext{Base.PipeEndpoint}, internal_stdout::IOContext{IOStream}, ignore_loaded_modules::Bool)
    @ Base ./loading.jl:1385
  [3] compilecache(pkg::Base.PkgId, path::String)
    @ Base ./loading.jl:1329
  [4] _require(pkg::Base.PkgId)
    @ Base ./loading.jl:1043
  [5] require(uuidkey::Base.PkgId)
    @ Base ./loading.jl:936
  [6] require(into::Module, mod::Symbol)
    @ Base ./loading.jl:923
  [7] top-level scope
    @ ~/.julia/packages/Plots/gYkEG/src/backends.jl:381
  [8] eval
    @ ./boot.jl:360 [inlined]
  [9] _initialize_backend(pkg::Plots.GRBackend)
    @ Plots ~/.julia/packages/Plots/gYkEG/src/backends.jl:380
 [10] backend(pkg::Plots.GRBackend)
    @ Plots ~/.julia/packages/Plots/gYkEG/src/backends.jl:245
 [11] backend(sym::Symbol)
    @ Plots ~/.julia/packages/Plots/gYkEG/src/backends.jl:255
 [12] load_default_backend()
    @ Plots ~/.julia/packages/Plots/gYkEG/src/backends.jl:200
 [13] backend()
    @ Plots ~/.julia/packages/Plots/gYkEG/src/backends.jl:233
 [14] top-level scope
    @ ~/.julia/packages/Plots/gYkEG/src/init.jl:110
 [15] include(mod::Module, _path::String)
    @ Base ./Base.jl:384
 [16] include(x::String)
    @ Plots ~/.julia/packages/Plots/gYkEG/src/Plots.jl:1
 [17] top-level scope
    @ ~/.julia/packages/Plots/gYkEG/src/Plots.jl:176
 [18] include
    @ ./Base.jl:384 [inlined]
 [19] include_package_for_output(pkg::Base.PkgId, input::String, depot_path::Vector{String}, dl_load_path::Vector{String}, load_path::Vector{String}, concrete_deps::Vector{Pair{Base.PkgId, UInt64}}, source::Nothing)
    @ Base ./loading.jl:1235
 [20] top-level scope
    @ none:1
 [21] eval
    @ ./boot.jl:360 [inlined]
 [22] eval(x::Expr)
    @ Base.MainInclude ./client.jl:446
 [23] top-level scope
    @ none:1
in expression starting at /root/.julia/packages/Plots/gYkEG/src/init.jl:110
in expression starting at /root/.julia/packages/Plots/gYkEG/src/Plots.jl:1

Stacktrace:
 [1] pkgerror(msg::String)
   @ Pkg.Types /buildworker/worker/package_linux64/build/usr/share/julia/stdlib/v1.6/Pkg/src/Types.jl:55
 [2] precompile(ctx::Pkg.Types.Context; internal_call::Bool, strict::Bool, warn_loaded::Bool, kwargs::Base.Iterators.Pairs{Union{}, Union{}, Tuple{}, NamedTuple{(), Tuple{}}})
   @ Pkg.API /buildworker/worker/package_linux64/build/usr/share/julia/stdlib/v1.6/Pkg/src/API.jl:1265
 [3] precompile
   @ /buildworker/worker/package_linux64/build/usr/share/julia/stdlib/v1.6/Pkg/src/API.jl:921 [inlined]
 [4] #precompile#196
   @ /buildworker/worker/package_linux64/build/usr/share/julia/stdlib/v1.6/Pkg/src/API.jl:919 [inlined]
 [5] precompile()
   @ Pkg.API /buildworker/worker/package_linux64/build/usr/share/julia/stdlib/v1.6/Pkg/src/API.jl:919
 [6] top-level scope
   @ none:1